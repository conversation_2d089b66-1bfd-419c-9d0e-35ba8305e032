services:
    app:
        build:
            context: .
            dockerfile: docker/${NODE_ENV}.dockerfile
        container_name: editor-service-app
        hostname: editor-service-app
        volumes:
            - ./:/app
        ports:
            - ${APP_PORT}:${APP_PORT}
            - 50055:50055
        networks:
            - skillspace-network
        env_file:
            - ./.env

    db:
        image: postgres:16-alpine
        container_name: editor-service-db
        hostname: editor-service-db
        environment:
            POSTGRES_DB: editor
            POSTGRES_USER: user
            POSTGRES_PASSWORD: password
        restart: unless-stopped
        ports:
            - '5017:5432'
        networks:
            - skillspace-network
        volumes:
            - db_data:/var/lib/postgresql/data

networks:
    skillspace-network:
        external: true

volumes:
    db_data:
