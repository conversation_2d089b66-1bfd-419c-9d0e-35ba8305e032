#!/usr/bin/env tsx

import { randomUUID } from 'node:crypto';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';

import { docs, draftPages } from '../src/drizzle/schema/index.js';

async function createDocument() {
    console.log('Подключение к базе данных...');

    const connectionString = 'postgresql://user:password@localhost:5017/editor';
    const pool = new Pool({ connectionString });
    const db = drizzle(pool);

    try {
        const docId = randomUUID();

        await db.insert(docs).values({
            id: docId,
            serviceId: 'manual-test-service',
            entityId: 'manual-test-entity',
            schoolId: 'manual-test-school',
            createdAt: new Date(),
            updatedAt: new Date(),
        });

        const pageId = randomUUID();
        await db.insert(draftPages).values({
            id: pageId,
            docId,
            ydoc: Buffer.from(''),
            createdAt: new Date(),
            updatedAt: new Date(),
        });

        console.log(`Draft Page ID: ${pageId}`);

        return {
            docId,
            pageId,
            createdAt: new Date(),
        };
    } catch (error) {
        console.error('Ошибка при создании документа:', error);
        throw error;
    } finally {
        await pool.end();
        console.log('Соединение с базой данных закрыто');
    }
}

// Основная функция
async function main() {
    try {
        console.log('Создание документа...');
        await createDocument();
        process.exit(0);
    } catch (error) {
        console.error('Ошибка:', error);
        process.exit(1);
    }
}

// Запуск только если файл выполняется напрямую
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export { createDocument };
