/* eslint-disable @typescript-eslint/no-require-imports */

// Полностью отключаем OpenTelemetry в тестах
process.env.OTEL_DISABLED = 'true';
process.env.OTEL_SDK_DISABLED = 'true';
process.env.OTEL_TRACES_EXPORTER = 'none';
process.env.OTEL_METRICS_EXPORTER = 'none';
process.env.OTEL_LOGS_EXPORTER = 'none';

// Mock OpenTelemetry API
jest.mock('@opentelemetry/api', () => ({
    createContextKey: jest.fn(() => Symbol('test-context-key')),
    trace: {
        getTracer: jest.fn(() => ({
            startSpan: jest.fn(() => ({
                setStatus: jest.fn(),
                setAttributes: jest.fn(),
                recordException: jest.fn(),
                end: jest.fn(),
            })),
        })),
    },
    metrics: {
        getMeter: jest.fn(() => ({
            createCounter: jest.fn(() => ({
                add: jest.fn(),
            })),
            createHistogram: jest.fn(() => ({
                record: jest.fn(),
            })),
            createGauge: jest.fn(() => ({
                record: jest.fn(),
            })),
        })),
    },
    context: {
        active: jest.fn(() => ({})),
        with: jest.fn((ctx, fn) => fn()),
    },
    propagation: {
        extract: jest.fn(),
        inject: jest.fn(),
        createBaggage: jest.fn(),
        getActiveBaggage: jest.fn(),
        setBaggage: jest.fn(),
    },
}));

// Mock OpenTelemetry Core
jest.mock('@opentelemetry/core', () => ({
    createContextKey: jest.fn(() => Symbol('test-context-key')),
    suppressTracing: jest.fn(),
    unsuppressTracing: jest.fn(),
    isTracingSuppressed: jest.fn(() => false),
}));

// Mock OpenTelemetry SDK
jest.mock('@opentelemetry/sdk-metrics', () => ({
    MeterProvider: jest.fn().mockImplementation(() => ({
        getMeter: jest.fn(() => ({
            createCounter: jest.fn(() => ({ add: jest.fn() })),
            createHistogram: jest.fn(() => ({ record: jest.fn() })),
            createGauge: jest.fn(() => ({ record: jest.fn() })),
        })),
        shutdown: jest.fn(() => Promise.resolve()),
        forceFlush: jest.fn(() => Promise.resolve()),
    })),
    PeriodicExportingMetricReader: jest.fn().mockImplementation(() => ({
        shutdown: jest.fn(() => Promise.resolve()),
        forceFlush: jest.fn(() => Promise.resolve()),
    })),
    ConsoleMetricExporter: jest.fn(),
}));

// Mock OTLP exporters
jest.mock('@opentelemetry/otlp-exporter-base', () => ({}));
jest.mock('@opentelemetry/exporter-metrics-otlp-http', () => ({
    OTLPMetricExporter: jest.fn().mockImplementation(() => ({
        export: jest.fn(),
        shutdown: jest.fn(() => Promise.resolve()),
    })),
}));

// Mock Skillspace tracing module
jest.mock('@skillspace/tracing', () => ({
    OpentelemetryModule: {
        forRoot: jest.fn(() => ({
            module: class MockOpentelemetryModule {},
            providers: [],
            exports: [],
        })),
    },
}));

jest.mock('@hocuspocus/transformer', () => ({
    TiptapTransformer: {
        toYdoc: jest.fn(() => ({
            getXmlFragment: jest.fn(() => ({
                toArray: jest.fn(() => []),
            })),
        })),
        fromYdoc: jest.fn(() => ({ type: 'doc', content: [] })),
    },
}));
