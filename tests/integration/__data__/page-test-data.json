{"basicPage": {"serviceId": "basic-service", "entityId": "basic-entity", "schoolId": "basic-school", "content": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "This is a basic test page content."}]}]}}, "complexPage": {"serviceId": "complex-service", "entityId": "complex-entity", "schoolId": "complex-school", "content": {"type": "doc", "content": [{"type": "heading", "attrs": {"level": 1}, "content": [{"type": "text", "text": "Complex Page Title"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "This is a complex page with multiple elements."}]}, {"type": "bulletList", "content": [{"type": "listItem", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "First item"}]}]}, {"type": "listItem", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Second item"}]}]}]}]}, "isLocked": false}, "lockedPage": {"serviceId": "locked-service", "entityId": "locked-entity", "schoolId": "locked-school", "content": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "This page is locked for editing."}]}]}, "isLocked": true}, "emptyPage": {"serviceId": "empty-service", "entityId": "empty-entity", "schoolId": "empty-school", "content": {"type": "doc", "content": []}}}