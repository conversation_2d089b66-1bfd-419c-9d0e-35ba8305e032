import { randomUUID } from 'node:crypto';

import { CreateDocumentDto } from '../../src/modules/editor/application/services/document.service';
import { app, documentService, pageService, userSession } from './test-setup';

describe('Document Resolver Integration Tests', () => {
    beforeAll(async () => {
        expect(app).toBeDefined();
        expect(documentService).toBeDefined();
        expect(pageService).toBeDefined();
        expect(userSession).toBeDefined();
    });

    describe('getDocument', () => {
        it('должен возвращать документ с пустым массивом pages если страницы не опубликованы', async () => {
            // Создаем документ с черновой страницей через сервис
            const createDocDto: CreateDocumentDto = {
                serviceId: 'test-service-unpublished',
                entityId: 'test-entity-unpublished',
                schoolId: 'test-school-unpublished',
            };

            const docId = randomUUID();
            await documentService.createDocumentWithDraftPage(createDocDto, docId);

            // Добавляем еще несколько черновых страниц
            await pageService.addPageToDocument(docId);
            await pageService.addPageToDocument(docId);

            // Проверяем, что есть черновые страницы
            const draftPages = await pageService.getDraftPagesByDocId(docId);
            expect(draftPages).toHaveLength(3);

            // Получаем документ через resolver
            const document = await userSession.getDocument(docId);

            expect(document).toBeDefined();
            expect(document.id).toBe(docId);
            expect(document.pages).toEqual([]); // Пустой массив, так как страницы не опубликованы
            expect(document.createdAt).toBeDefined();
            expect(document.updatedAt).toBeDefined();
        });

        it('должен возвращать документ с опубликованными страницами', async () => {
            // Создаем документ с черновой страницей
            const createDocDto: CreateDocumentDto = {
                serviceId: 'test-service-published',
                entityId: 'test-entity-published',
                schoolId: 'test-school-published',
            };

            const docId = randomUUID();
            await documentService.createDocumentWithDraftPage(createDocDto, docId);

            // Добавляем еще страницы
            await pageService.addPageToDocument(docId);

            // Публикуем документ
            await documentService.publishDocument(docId);

            // Получаем документ через resolver
            const document = await userSession.getDocument(docId);

            expect(document).toBeDefined();
            expect(document.id).toBe(docId);
            expect(document.pages).toHaveLength(2);
            expect(document.pages[0]).toHaveProperty('id');
            expect(document.pages[0]).toHaveProperty('content');
            expect(document.pages[0]).toHaveProperty('createdAt');
            expect(document.pages[0]).toHaveProperty('updatedAt');
        });

        it('должен выбросить ошибку для несуществующего документа', async () => {
            const nonExistentId = randomUUID();

            await expect(userSession.getDocument(nonExistentId)).rejects.toThrow();
        });
    });

    describe('addPage', () => {
        it('должен увеличивать массив draftPageIds при добавлении страницы', async () => {
            // Создаем документ
            const createDocDto: CreateDocumentDto = {
                serviceId: 'test-service-add-page',
                entityId: 'test-entity-add-page',
                schoolId: 'test-school-add-page',
            };

            const docId = randomUUID();
            await documentService.createDocumentWithDraftPage(createDocDto, docId);

            // Проверяем начальное состояние
            const initialDraftPages = await pageService.getDraftPagesByDocId(docId);
            expect(initialDraftPages).toHaveLength(1);

            // Добавляем страницу через resolver
            const result = await userSession.addPage(docId);

            expect(result).toBeDefined();
            expect(result.id).toBe(docId);
            expect(result.draftPageIds).toHaveLength(2);
            expect(result.createdAt).toBeDefined();
            expect(result.updatedAt).toBeDefined();

            // Проверяем, что страница действительно добавилась
            const updatedDraftPages = await pageService.getDraftPagesByDocId(docId);
            expect(updatedDraftPages).toHaveLength(2);
            expect(result.draftPageIds.sort()).toEqual(updatedDraftPages.map((p) => p.id).sort());
        });

        it('должен выбросить ошибку для несуществующего документа', async () => {
            const nonExistentId = randomUUID();

            await expect(userSession.addPage(nonExistentId)).rejects.toThrow();
        });
    });

    describe('publishDocument', () => {
        it('должен сделать страницы доступными через getDocument после публикации', async () => {
            // Создаем документ с черновыми страницами
            const createDocDto: CreateDocumentDto = {
                serviceId: 'test-service-publish',
                entityId: 'test-entity-publish',
                schoolId: 'test-school-publish',
            };

            const docId = randomUUID();
            await documentService.createDocumentWithDraftPage(createDocDto, docId);
            await pageService.addPageToDocument(docId);
            await pageService.addPageToDocument(docId);

            // Проверяем, что документ пустой до публикации
            const documentBefore = await userSession.getDocument(docId);
            expect(documentBefore.pages).toHaveLength(0);

            // Публикуем документ через resolver
            const publishResult = await userSession.publishDocument(docId);
            expect(publishResult).toBe(true);

            // Проверяем, что страницы стали доступны
            const documentAfter = await userSession.getDocument(docId);
            expect(documentAfter.pages).toHaveLength(3);
        });

        it('должен выбросить ошибку для несуществующего документа', async () => {
            const nonExistentId = randomUUID();

            await expect(userSession.publishDocument(nonExistentId)).rejects.toThrow();
        });
    });

    describe('deletePage', () => {
        it('должен сделать страницу недоступной через getDocument после удаления', async () => {
            // Создаем документ с черновыми страницами
            const createDocDto: CreateDocumentDto = {
                serviceId: 'test-service-delete-page',
                entityId: 'test-entity-delete-page',
                schoolId: 'test-school-delete-page',
            };

            const docId = randomUUID();
            await documentService.createDocumentWithDraftPage(createDocDto, docId);
            await pageService.addPageToDocument(docId);
            await pageService.addPageToDocument(docId);

            // Публикуем документ
            await documentService.publishDocument(docId);

            // Проверяем, что есть 3 страницы
            const documentBefore = await userSession.getDocument(docId);
            expect(documentBefore.pages).toHaveLength(3);

            // Удаляем одну страницу
            const pageToDelete = documentBefore.pages[0];
            const deleteResult = await userSession.deletePage(pageToDelete.id);
            expect(deleteResult).toBe(true);

            // Проверяем, что страница стала недоступна
            const documentAfter = await userSession.getDocument(docId);
            expect(documentAfter.pages).toHaveLength(2);
            expect(documentAfter.pages.find((p) => p.id === pageToDelete.id)).toBeUndefined();
        });

        it('должен корректно обработать удаление несуществующей страницы', async () => {
            const nonExistentId = randomUUID();

            // deletePage является идемпотентной операцией и не должна выбрасывать ошибку
            const result = await userSession.deletePage(nonExistentId);
            expect(result).toBe(true);
        });
    });
});
