import { randomUUID } from 'node:crypto';
import * as Y from 'yjs';

import { CreateDocumentDto } from '../../src/modules/editor/application/services/document.service';
import { documentService, pageService } from './test-setup';
import { RealHocuspocusTestClient } from './utils/real-hocuspocus-client';

describe('Simple Binary Updates Tests', () => {
    const baseUrl = 'http://localhost:3000'; // Используем тестовый сервер из test-setup

    it('должен создать RealHocuspocusTestClient и проверить его методы', async () => {
        // 1. Создаем документ с черновой страницей
        const createDocDto: CreateDocumentDto = {
            serviceId: 'client-test-service',
            entityId: 'client-test-entity',
            schoolId: 'client-test-school',
        };

        const docId = randomUUID();
        await documentService.createDocumentWithDraftPage(createDocDto, docId);

        // Получаем созданную черновую страницу
        const draftPages = await pageService.getDraftPagesByDocId(docId);
        const page = draftPages[0];

        // 2. Создаем клиент
        const client = new RealHocuspocusTestClient({
            url: baseUrl,
            documentName: page.id,
        });

        // 3. Проверяем начальное состояние
        expect(client.isConnected()).toBe(false);
        expect(client.isSynced()).toBe(false);

        // 4. Проверяем Y.Doc
        const ydoc = client.getYDoc();
        expect(ydoc).toBeInstanceOf(Y.Doc);

        // 5. Проверяем бинарное состояние
        const binaryState = client.getBinaryState();
        expect(binaryState).toBeInstanceOf(Uint8Array);

        // 6. Добавляем контент
        client.insertText('Тестовый текст');

        // 7. Проверяем, что контент добавился
        const content = client.getContent();
        expect(content.type).toBe('doc');
        expect(content.content).toBeDefined();

        // 8. Проверяем, что бинарное состояние изменилось
        const newBinaryState = client.getBinaryState();
        expect(newBinaryState.length).toBeGreaterThan(binaryState.length);
    });

    it('должен работать с различными типами контента', async () => {
        const client = new RealHocuspocusTestClient({
            url: baseUrl,
            documentName: 'test-doc-id',
        });

        // Тестируем различные методы
        client.insertHeading('Заголовок', 1);
        client.insertText('Параграф');
        client.insertBoldText('Жирный текст');
        client.insertItalicText('Курсивный текст');
        client.insertBulletList(['Элемент 1', 'Элемент 2']);
        client.insertOrderedList(['Пункт 1', 'Пункт 2']);
        client.insertTaskList([
            { text: 'Задача 1', checked: true },
            { text: 'Задача 2', checked: false },
        ]);
        client.insertTable(2, 2);
        client.insertCodeBlock('console.log("test");', 'javascript');
        client.insertBlockquote('Цитата');
        client.insertImage('https://example.com/image.jpg', 'Тестовое изображение');
        client.insertHorizontalRule();

        // Проверяем, что все добавилось
        const content = client.getContent();
        expect(content.type).toBe('doc');
        expect(content.content).toBeDefined();

        // Проверяем бинарное состояние
        const binaryState = client.getBinaryState();
        expect(binaryState).toBeInstanceOf(Uint8Array);
        expect(binaryState.length).toBeGreaterThan(0);

        // Очищаем содержимое
        client.clearContent();
        const clearedContent = client.getContent();
        expect(clearedContent.content).toEqual([]);
    });
});
