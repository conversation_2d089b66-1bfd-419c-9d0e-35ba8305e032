import { randomUUID } from 'node:crypto';
import { NotFoundException } from '@nestjs/common';

import { CreateDocumentDto } from '../../src/modules/editor/application/services/document.service';
import { app, documentService, pageService } from './test-setup';

describe('DocumentService Integration Tests', () => {
    beforeAll(async () => {
        expect(app).toBeDefined();
        expect(documentService).toBeDefined();
        expect(pageService).toBeDefined();
    });

    describe('createDocumentWithDraftPage', () => {
        it('должен создать документ с первой черновой страницей', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'test-service',
                entityId: 'test-entity',
                schoolId: 'test-school',
            };

            const docId = randomUUID();
            await documentService.createDocumentWithDraftPage(createDocDto, docId);

            // Проверяем, что документ создался
            const doc = await documentService.findByIdOrThrow(docId);
            expect(doc).toBeDefined();
            expect(doc.id).toBe(docId);
            expect(doc.serviceId).toBe('test-service');
            expect(doc.entityId).toBe('test-entity');
            expect(doc.schoolId).toBe('test-school');
            expect(doc.createdAt).toBeDefined();
            expect(doc.updatedAt).toBeDefined();

            // Проверяем, что создалась черновая страница
            const draftPages = await pageService.getDraftPagesByDocId(docId);
            expect(draftPages).toHaveLength(1);
            expect(draftPages[0].docId).toBe(docId);
            expect(draftPages[0].ydoc).toBeNull();
        });

        it('должен создать документ с предустановленным docId', async () => {
            const customDocId = randomUUID();
            const createDocDto: CreateDocumentDto = {
                serviceId: 'custom-service',
                entityId: 'custom-entity',
                schoolId: 'custom-school',
            };

            await documentService.createDocumentWithDraftPage(createDocDto, customDocId);

            // Проверяем, что документ создался с правильным ID
            const doc = await documentService.findByIdOrThrow(customDocId);
            expect(doc).toBeDefined();
            expect(doc.id).toBe(customDocId);
            expect(doc.serviceId).toBe('custom-service');
            expect(doc.entityId).toBe('custom-entity');
            expect(doc.schoolId).toBe('custom-school');

            // Проверяем, что создалась черновая страница
            const draftPages = await pageService.getDraftPagesByDocId(customDocId);
            expect(draftPages).toHaveLength(1);
        });

        it('должен обработать ошибку при создании документа с дублирующимся ID', async () => {
            const duplicateDocId = randomUUID();
            const createDocDto: CreateDocumentDto = {
                serviceId: 'duplicate-service',
                entityId: 'duplicate-entity',
                schoolId: 'duplicate-school',
            };

            // Создаем первый документ
            await documentService.createDocumentWithDraftPage(createDocDto, duplicateDocId);

            // Пытаемся создать второй документ с тем же ID
            await expect(documentService.createDocumentWithDraftPage(createDocDto, duplicateDocId)).rejects.toThrow();
        });
    });

    describe('publishDocument', () => {
        it('должен опубликовать документ (конвертировать черновики в публичные страницы)', async () => {
            // Создаем документ с черновой страницей
            const createDocDto: CreateDocumentDto = {
                serviceId: 'publish-service',
                entityId: 'publish-entity',
                schoolId: 'publish-school',
            };

            const docId = randomUUID();
            await documentService.createDocumentWithDraftPage(createDocDto, docId);

            // Добавляем еще одну черновую страницу
            await pageService.addPageToDocument(docId);

            // Проверяем, что есть черновые страницы
            const draftPages = await pageService.getDraftPagesByDocId(docId);
            expect(draftPages).toHaveLength(2);

            // Публикуем документ
            await documentService.publishDocument(docId);

            // Проверяем, что создались публичные страницы
            const publicPages = await pageService.getPublicPagesByDocId(docId);
            expect(publicPages).toHaveLength(2);
            expect(publicPages.map((p) => p.id).sort()).toEqual(draftPages.map((p) => p.id).sort());
        });

        it('должен выбросить NotFoundException для несуществующего документа', async () => {
            const nonExistentId = randomUUID();

            await expect(documentService.publishDocument(nonExistentId)).rejects.toThrow(NotFoundException);
        });
    });

    describe('forceDelete', () => {
        it('должен удалить документ со всеми связанными страницами', async () => {
            // Создаем документ с черновой страницей
            const createDocDto: CreateDocumentDto = {
                serviceId: 'delete-service',
                entityId: 'delete-entity',
                schoolId: 'delete-school',
            };

            const docId = randomUUID();
            await documentService.createDocumentWithDraftPage(createDocDto, docId);

            // Добавляем еще страницы
            await pageService.addPageToDocument(docId);
            await pageService.addPageToDocument(docId);

            // Публикуем документ
            await documentService.publishDocument(docId);

            // Проверяем, что есть черновые и публичные страницы
            const draftPages = await pageService.getDraftPagesByDocId(docId);
            const publicPages = await pageService.getPublicPagesByDocId(docId);
            expect(draftPages).toHaveLength(3);
            expect(publicPages).toHaveLength(3);

            // Удаляем документ
            await documentService.forceDelete(docId);

            // Проверяем, что документ удален
            await expect(documentService.findByIdOrThrow(docId)).rejects.toThrow(NotFoundException);

            // Проверяем, что все страницы удалены
            const remainingDraftPages = await pageService.getDraftPagesByDocId(docId);
            const remainingPublicPages = await pageService.getPublicPagesByDocId(docId);
            expect(remainingDraftPages).toHaveLength(0);
            expect(remainingPublicPages).toHaveLength(0);
        });

        it('должен выбросить NotFoundException при попытке удалить несуществующий документ', async () => {
            const nonExistentId = randomUUID();

            // forceDelete не проверяет существование документа, он просто удаляет записи
            // Поэтому этот тест должен проходить без ошибок
            await expect(documentService.forceDelete(nonExistentId)).resolves.not.toThrow();
        });
    });

    describe('createManyDocs', () => {
        it('должен создать несколько документов с черновыми страницами', async () => {
            const createDocsDto = [
                {
                    serviceId: 'bulk-service-1',
                    entityId: 'bulk-entity-1',
                    schoolId: 'bulk-school-1',
                },
                {
                    serviceId: 'bulk-service-2',
                    entityId: 'bulk-entity-2',
                    schoolId: 'bulk-school-2',
                },
            ];

            const docs = await documentService.createManyDocs(createDocsDto);

            expect(docs).toBeDefined();
            expect(docs).toHaveLength(2);

            // Проверяем, что документы действительно созданы в БД
            for (let i = 0; i < docs.length; i++) {
                const doc = docs[i];
                const foundDoc = await documentService.findByIdOrThrow(doc.docId);
                expect(foundDoc).toBeDefined();
                expect(foundDoc.id).toBe(doc.docId);
                expect(foundDoc.serviceId).toBe(createDocsDto[i].serviceId);
                expect(foundDoc.entityId).toBe(createDocsDto[i].entityId);
                expect(foundDoc.schoolId).toBe(createDocsDto[i].schoolId);

                // Проверяем, что создалась черновая страница
                const draftPages = await pageService.getDraftPagesByDocId(doc.docId);
                expect(draftPages).toHaveLength(1);
            }
        });

        it('должен создать пустой ответ при передаче пустого массива', async () => {
            const docs = await documentService.createManyDocs([]);

            expect(docs).toBeDefined();
            expect(docs).toHaveLength(0);
        });
    });

    describe('removeManyDocs', () => {
        it('должен удалить несколько документов со всеми связанными страницами', async () => {
            const createDocsDto = [
                {
                    serviceId: 'remove-service-1',
                    entityId: 'remove-entity-1',
                    schoolId: 'remove-school-1',
                },
                {
                    serviceId: 'remove-service-2',
                    entityId: 'remove-entity-2',
                    schoolId: 'remove-school-2',
                },
            ];

            const docs = await documentService.createManyDocs(createDocsDto);
            expect(docs).toHaveLength(2);

            // Проверяем, что документы существуют
            for (const doc of docs) {
                const foundDoc = await documentService.findByIdOrThrow(doc.docId);
                expect(foundDoc).toBeDefined();
            }

            // Удаляем документы
            const removeResponse = await documentService.forceDeleteManyDocs(docs.map((doc) => doc.docId));

            expect(removeResponse).toBeDefined();
            expect(removeResponse.success).toBe(true);
            expect(removeResponse.removedDocIds).toHaveLength(2);

            // Проверяем, что документы удалены
            for (const doc of docs) {
                await expect(documentService.findByIdOrThrow(doc.docId)).rejects.toThrow('Документ не найден');
            }
        });

        it('должен корректно обработать удаление несуществующих документов', async () => {
            // forceDeleteManyDocs не проверяет существование документов, он просто удаляет записи
            const removeResponse = await documentService.forceDeleteManyDocs([randomUUID(), randomUUID()]);

            expect(removeResponse).toBeDefined();
            expect(removeResponse.success).toBe(true);
            expect(removeResponse.removedDocIds).toHaveLength(2);
        });
    });

    describe('findById', () => {
        it('должен найти существующий документ', async () => {
            const createDocDto: CreateDocumentDto = {
                serviceId: 'find-service',
                entityId: 'find-entity',
                schoolId: 'find-school',
            };

            const docId = randomUUID();
            await documentService.createDocumentWithDraftPage(createDocDto, docId);
            const foundDoc = await documentService.findByIdOrThrow(docId);

            expect(foundDoc.id).toBe(docId);
            expect(foundDoc.serviceId).toBe('find-service');
            expect(foundDoc.entityId).toBe('find-entity');
            expect(foundDoc.schoolId).toBe('find-school');
        });

        it('должен выбросить NotFoundException для несуществующего документа', async () => {
            const nonExistentId = randomUUID();

            await expect(documentService.findByIdOrThrow(nonExistentId)).rejects.toThrow(NotFoundException);
            await expect(documentService.findByIdOrThrow(nonExistentId)).rejects.toThrow(
                `Документ не найден (docId: ${nonExistentId})`,
            );
        });
    });

    describe('Сценарий: Публикация, редактирование, повторная публикация', () => {
        it('должен корректно обрабатывать изменения в черновиках после публикации', async () => {
            // Создаем документ с черновой страницей
            const createDocDto: CreateDocumentDto = {
                serviceId: 'edit-service',
                entityId: 'edit-entity',
                schoolId: 'edit-school',
            };

            const docId = randomUUID();
            await documentService.createDocumentWithDraftPage(createDocDto, docId);

            // Публикуем документ
            await documentService.publishDocument(docId);

            // Проверяем, что страницы доступны через pageService.getPublicPagesByDocId
            const publicPagesAfterFirstPublish = await pageService.getPublicPagesByDocId(docId);
            expect(publicPagesAfterFirstPublish).toHaveLength(1);

            // Добавляем новую страницу (это изменение в черновике)
            await pageService.addPageToDocument(docId);

            // Проверяем, что изменения НЕ доступны через pageService.getPublicPagesByDocId
            const publicPagesAfterEdit = await pageService.getPublicPagesByDocId(docId);
            expect(publicPagesAfterEdit).toHaveLength(1); // Все еще 1 страница

            // Но черновики должны содержать 2 страницы
            const draftPagesAfterEdit = await pageService.getDraftPagesByDocId(docId);
            expect(draftPagesAfterEdit).toHaveLength(2);

            // Снова публикуем документ
            await documentService.publishDocument(docId);

            // Теперь изменения должны стать доступны через pageService.getPublicPagesByDocId
            const publicPagesAfterSecondPublish = await pageService.getPublicPagesByDocId(docId);
            expect(publicPagesAfterSecondPublish).toHaveLength(2);
        });
    });
});
