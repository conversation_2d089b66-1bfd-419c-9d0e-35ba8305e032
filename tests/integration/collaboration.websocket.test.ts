import { randomUUID } from 'node:crypto';

import { DraftPage } from '../../src/drizzle/schema';
import { documentService, pageService } from './test-setup';
import { HocuspocusTestClient } from './utils/hocuspocus-client';
import { TestServer } from './utils/test-server';

describe('Collaboration WebSocket Integration Tests', () => {
    let testServer: TestServer;
    let baseUrl: string;
    let docId: string;
    let draftPage: DraftPage;

    beforeAll(async () => {
        testServer = new TestServer();
        const { port } = await testServer.start();
        baseUrl = `http://localhost:${port}`;
    });

    afterAll(async () => {
        if (testServer) {
            await testServer.stop();
        }
    });

    beforeEach(async () => {
        docId = randomUUID();
        await documentService.createDocumentWithDraftPage(
            {
                serviceId: 'content-test-service',
                entityId: 'content-test-entity',
                schoolId: 'content-test-school',
            },
            docId,
        );

        const draftPages = await pageService.getDraftPagesByDocId(docId);
        draftPage = draftPages[0];
    });

    afterEach(() => {
        draftPage = null;
        docId = null;
    });

    it('должен установить WebSocket соединение с сервером', async () => {
        const client = new HocuspocusTestClient({
            url: baseUrl,
            documentName: draftPage.id,
        });

        await expect(client.connect()).resolves.not.toThrow();
        expect(client.isConnected()).toBe(true);

        client.disconnect();
    });

    it('должен обработать подключение к несуществующему документу', async () => {
        const client = new HocuspocusTestClient({
            url: baseUrl,
            documentName: 'non-existent-page-id',
        });

        await expect(client.connect()).resolves.not.toThrow();
        expect(client.isConnected()).toBe(true);

        const content = client.getContent();
        expect(content.type).toBe('doc');

        client.disconnect();
    });

    it('должен отклонить подключение к несуществующему порту', async () => {
        const client = new HocuspocusTestClient({
            url: 'http://localhost:99999',
            documentName: 'test-page-id',
        });

        await expect(client.connect()).rejects.toThrow();
    });

    it('должен загрузить существующий документ при подключении', async () => {
        const client = new HocuspocusTestClient({
            url: baseUrl,
            documentName: draftPage.id,
        });

        await client.connect();
        await client.waitForSync(500);

        const loadedContent = client.getContent();
        expect(loadedContent).toBeDefined();
        expect(loadedContent.type).toBe('doc');

        client.disconnect();
    });

    it('должен синхронизировать изменения между двумя клиентами', async () => {
        const client1 = new HocuspocusTestClient({
            url: baseUrl,
            documentName: draftPage.id,
        });

        const client2 = new HocuspocusTestClient({
            url: baseUrl,
            documentName: draftPage.id,
        });

        await Promise.all([client1.connect(), client2.connect()]);

        // For now, just test that both clients can connect to the same document
        // Real Y.js synchronization would require proper protocol implementation
        expect(client1.isConnected()).toBe(true);
        expect(client2.isConnected()).toBe(true);

        client1.disconnect();
        client2.disconnect();
    });

    // TODO: Добавить обновление черновика с бинарными данными
    it('должен сохранить изменения в базе данных', async () => {
        const client = new HocuspocusTestClient({
            url: baseUrl,
            documentName: draftPage.id,
        });

        await client.connect();

        // For now, just test that we can connect and the page exists
        // Real persistence would require proper Y.js protocol implementation
        expect(client.isConnected()).toBe(true);

        client.disconnect();

        // Verify the page still exists in database
        const retrievedPage = await pageService.findDraftById(draftPage.id);
        expect(retrievedPage).toBeDefined();
        expect(retrievedPage.id).toBe(draftPage.id);
        expect(retrievedPage.docId).toBe(docId);
    });
});
