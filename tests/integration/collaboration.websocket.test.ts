import { randomUUID } from 'node:crypto';
import * as WebSocket from 'ws';

import { DraftPage } from '../../src/drizzle/schema';
import { documentService, pageService } from './test-setup';
import { HocuspocusTestClient } from './utils/hocuspocus-client';
import { RealHocuspocusTestClient } from './utils/real-hocuspocus-client';
import { TestServer } from './utils/test-server';

describe('Collaboration WebSocket Integration Tests', () => {
    let testServer: TestServer;
    let baseUrl: string;
    let docId: string;
    let draftPage: DraftPage;
    let serverPort: string;

    beforeAll(async () => {
        testServer = new TestServer();
        const { port } = await testServer.start();
        serverPort = port.toString();
        baseUrl = `http://localhost:${port}`;
    });

    afterAll(async () => {
        if (testServer) {
            await testServer.stop();
        }
    });

    beforeEach(async () => {
        docId = randomUUID();
        await documentService.createDocumentWithDraftPage(
            {
                serviceId: 'content-test-service',
                entityId: 'content-test-entity',
                schoolId: 'content-test-school',
            },
            docId,
        );

        const draftPages = await pageService.getDraftPagesByDocId(docId);
        draftPage = draftPages[0];
    });

    afterEach(() => {
        draftPage = null;
        docId = null;
    });

    describe('Базовые WebSocket соединения', () => {
        it('должен запустить HTTP сервер', async () => {
            expect(testServer.getApp()).toBeDefined();
            expect(baseUrl).toContain('localhost');
            expect(Number(serverPort)).toBeGreaterThan(0);
        });

        it('должен принимать WebSocket соединения', async () => {
            return new Promise<void>((resolve, reject) => {
                const wsUrl = `${baseUrl.replace('http', 'ws')}/collab`;
                const ws = new WebSocket(wsUrl);

                const timeout = setTimeout(() => {
                    ws.close();
                    reject(new Error('Connection timeout'));
                }, 5000);

                ws.on('open', () => {
                    clearTimeout(timeout);
                    ws.close();
                    resolve();
                });

                ws.on('error', (error) => {
                    clearTimeout(timeout);
                    reject(error);
                });
            });
        });

        it('должен обработать неправильный URL WebSocket', async () => {
            return new Promise<void>((resolve, reject) => {
                try {
                    const ws = new WebSocket('ws://localhost:99999/collab');

                    const timeout = setTimeout(() => {
                        ws.close();
                        resolve(); // Timeout is expected for invalid URL
                    }, 2000);

                    ws.on('open', () => {
                        clearTimeout(timeout);
                        ws.close();
                        reject(new Error('Should not connect to invalid URL'));
                    });

                    ws.on('error', () => {
                        clearTimeout(timeout);
                        resolve(); // Error is expected
                    });
                } catch (error) {
                    // URL validation error is also expected
                    resolve();
                }
            });
        });
    });

    describe('Hocuspocus клиент соединения', () => {
        it('должен установить WebSocket соединение с сервером', async () => {
            const client = new HocuspocusTestClient({
                url: baseUrl,
                documentName: draftPage.id,
            });

            await expect(client.connect()).resolves.not.toThrow();
            expect(client.isConnected()).toBe(true);

            client.disconnect();
        });

        it('должен обработать подключение к несуществующему документу', async () => {
            const client = new HocuspocusTestClient({
                url: baseUrl,
                documentName: 'non-existent-page-id',
            });

            await expect(client.connect()).resolves.not.toThrow();
            expect(client.isConnected()).toBe(true);

            const content = client.getContent();
            expect(content.type).toBe('doc');

            client.disconnect();
        });

        it('должен отклонить подключение к несуществующему порту', async () => {
            const client = new HocuspocusTestClient({
                url: 'http://localhost:99999',
                documentName: 'test-page-id',
            });

            await expect(client.connect()).rejects.toThrow();
        });

        it('должен загрузить существующий документ при подключении', async () => {
            const client = new HocuspocusTestClient({
                url: baseUrl,
                documentName: draftPage.id,
            });

            await client.connect();
            await client.waitForSync(500);

            const loadedContent = client.getContent();
            expect(loadedContent).toBeDefined();
            expect(loadedContent.type).toBe('doc');

            client.disconnect();
        });

        it('должен синхронизировать изменения между двумя клиентами', async () => {
            const client1 = new HocuspocusTestClient({
                url: baseUrl,
                documentName: draftPage.id,
            });

            const client2 = new HocuspocusTestClient({
                url: baseUrl,
                documentName: draftPage.id,
            });

            await Promise.all([client1.connect(), client2.connect()]);

            expect(client1.isConnected()).toBe(true);
            expect(client2.isConnected()).toBe(true);

            client1.disconnect();
            client2.disconnect();
        });

        it('должен сохранить изменения в базе данных', async () => {
            const client = new HocuspocusTestClient({
                url: baseUrl,
                documentName: draftPage.id,
            });

            await client.connect();
            expect(client.isConnected()).toBe(true);

            client.disconnect();

            // Verify the page still exists in database
            const retrievedPage = await pageService.findDraftById(draftPage.id);
            expect(retrievedPage).toBeDefined();
            expect(retrievedPage.id).toBe(draftPage.id);
            expect(retrievedPage.docId).toBe(docId);
        });
    });

    describe('Бинарные Y.js обновления', () => {
        it('должен создать RealHocuspocusTestClient и проверить его методы', async () => {
            const client = new RealHocuspocusTestClient({
                url: baseUrl,
                documentName: draftPage.id,
            });

            // Проверяем начальное состояние
            expect(client.isConnected()).toBe(false);
            expect(client.isSynced()).toBe(false);

            // Проверяем Y.Doc
            const ydoc = client.getYDoc();
            expect(ydoc).toBeDefined();

            // Проверяем бинарное состояние
            const binaryState = client.getBinaryState();
            expect(binaryState).toBeInstanceOf(Uint8Array);

            // Добавляем контент
            client.insertText('Тестовый текст');

            // Проверяем, что контент добавился
            const content = client.getContent();
            expect(content.type).toBe('doc');
            expect(content.content).toBeDefined();

            // Проверяем, что бинарное состояние изменилось
            const newBinaryState = client.getBinaryState();
            expect(newBinaryState.length).toBeGreaterThan(binaryState.length);
        });

        it('должен работать с различными типами контента', async () => {
            const client = new RealHocuspocusTestClient({
                url: baseUrl,
                documentName: draftPage.id,
            });

            // Тестируем различные методы
            client.insertHeading('Заголовок', 1);
            client.insertText('Параграф');
            client.insertBoldText('Жирный текст');
            client.insertItalicText('Курсивный текст');
            client.insertBulletList(['Элемент 1', 'Элемент 2']);
            client.insertOrderedList(['Пункт 1', 'Пункт 2']);
            client.insertTaskList([
                { text: 'Задача 1', checked: true },
                { text: 'Задача 2', checked: false },
            ]);
            client.insertTable(2, 2);
            client.insertCodeBlock('console.log("test");', 'javascript');
            client.insertBlockquote('Цитата');
            client.insertImage('https://example.com/image.jpg', 'Тестовое изображение');
            client.insertHorizontalRule();

            // Проверяем, что все добавилось
            const content = client.getContent();
            expect(content.type).toBe('doc');
            expect(content.content).toBeDefined();

            // Проверяем бинарное состояние
            const binaryState = client.getBinaryState();
            expect(binaryState).toBeInstanceOf(Uint8Array);
            expect(binaryState.length).toBeGreaterThan(0);

            // Очищаем содержимое
            client.clearContent();
            const clearedContent = client.getContent();
            expect(clearedContent.content).toEqual([]);
        });

        it('должен обновить черновик страницы с бинарными данными', async () => {
            const client = new RealHocuspocusTestClient({
                url: baseUrl,
                documentName: draftPage.id,
            });

            await client.connect();
            expect(client.isConnected()).toBe(true);

            // Ждем синхронизации
            await client.waitForSync(3000);

            // Вносим изменения через Y.js обновления
            client.insertHeading('Тестовый заголовок', 1);
            await new Promise((resolve) => setTimeout(resolve, 500));

            client.insertBoldText('Жирный текст');
            await new Promise((resolve) => setTimeout(resolve, 500));

            client.insertItalicText('Курсивный текст');
            await new Promise((resolve) => setTimeout(resolve, 500));

            client.insertBulletList(['Первый элемент', 'Второй элемент', 'Третий элемент']);
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Получаем бинарное состояние
            const binaryState = client.getBinaryState();
            expect(binaryState).toBeInstanceOf(Uint8Array);
            expect(binaryState.length).toBeGreaterThan(0);

            // Ждем сохранения изменений на сервере
            await new Promise((resolve) => setTimeout(resolve, 2000));

            client.disconnect();

            // Получаем черновую страницу и проверяем, что данные сохранились
            const updatedPage = await pageService.findDraftById(draftPage.id);
            expect(updatedPage).toBeDefined();
            expect(updatedPage.id).toBe(draftPage.id);
            expect(updatedPage.docId).toBe(docId);
        });
    });

    describe('Совместное редактирование', () => {
        it('должен синхронизировать изменения между двумя реальными клиентами', async () => {
            const client1 = new RealHocuspocusTestClient({
                url: baseUrl,
                documentName: draftPage.id,
            });

            const client2 = new RealHocuspocusTestClient({
                url: baseUrl,
                documentName: draftPage.id,
            });

            await Promise.all([client1.connect(), client2.connect()]);

            expect(client1.isConnected()).toBe(true);
            expect(client2.isConnected()).toBe(true);

            // Ждем синхронизации
            await Promise.all([client1.waitForSync(3000), client2.waitForSync(3000)]);

            // Первый клиент добавляет контент
            client1.insertHeading('Заголовок от клиента 1', 1);
            await new Promise((resolve) => setTimeout(resolve, 500));

            client1.insertText('Текст от первого клиента');
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Второй клиент добавляет контент
            client2.insertHeading('Заголовок от клиента 2', 2);
            await new Promise((resolve) => setTimeout(resolve, 500));

            client2.insertText('Текст от второго клиента');
            await new Promise((resolve) => setTimeout(resolve, 500));

            // Проверяем, что оба клиента имеют контент
            const content1 = client1.getContent();
            const content2 = client2.getContent();

            expect(content1.type).toBe('doc');
            expect(content2.type).toBe('doc');
            expect(content1.content).toBeDefined();
            expect(content2.content).toBeDefined();

            // Ждем финальной синхронизации
            await new Promise((resolve) => setTimeout(resolve, 2000));

            client1.disconnect();
            client2.disconnect();

            // Проверяем результат через pageService
            const finalPage = await pageService.findDraftById(draftPage.id);
            expect(finalPage).toBeDefined();
            expect(finalPage.id).toBe(draftPage.id);
            expect(finalPage.docId).toBe(docId);
        });

        it('должен сохранить бинарные данные Y.js в базе данных', async () => {
            const client = new RealHocuspocusTestClient({
                url: baseUrl,
                documentName: draftPage.id,
            });

            await client.connect();
            await client.waitForSync(3000);

            // Добавляем контент
            client.insertHeading('Заголовок для проверки бинарных данных', 1);
            client.insertText('Параграф с текстом для проверки сохранения');
            client.insertBulletList(['Элемент 1', 'Элемент 2']);

            // Получаем бинарное состояние
            const binaryState = client.getBinaryState();
            expect(binaryState).toBeInstanceOf(Uint8Array);
            expect(binaryState.length).toBeGreaterThan(0);

            // Ждем сохранения
            await new Promise((resolve) => setTimeout(resolve, 2000));

            client.disconnect();

            // Проверяем, что данные сохранились
            const savedPage = await pageService.findDraftById(draftPage.id);
            expect(savedPage).toBeDefined();
            expect(savedPage.docId).toBe(docId);
        });
    });

    describe('Обработка сообщений WebSocket', () => {
        it('должен подключиться к существующему документу', async () => {
            return new Promise<void>((resolve, reject) => {
                const wsUrl = `${baseUrl.replace('http', 'ws')}/collab`;
                const ws = new WebSocket(wsUrl);

                const timeout = setTimeout(() => {
                    ws.close();
                    reject(new Error('Document connection timeout'));
                }, 10000);

                ws.on('open', () => {
                    clearTimeout(timeout);
                    ws.close();
                    resolve();
                });

                ws.on('message', (_data) => {
                    clearTimeout(timeout);
                    ws.close();
                    resolve();
                });

                ws.on('error', (error) => {
                    clearTimeout(timeout);
                    reject(error);
                });
            });
        });

        it('должен обработать сообщения от сервера', async () => {
            return new Promise<void>((resolve, reject) => {
                const wsUrl = `${baseUrl.replace('http', 'ws')}/collab`;
                const ws = new WebSocket(wsUrl);

                const timeout = setTimeout(() => {
                    ws.close();
                    reject(new Error('Message timeout'));
                }, 5000);

                ws.on('open', () => {
                    clearTimeout(timeout);
                    ws.close();
                    resolve();
                });

                ws.on('message', (_data) => {
                    clearTimeout(timeout);
                    ws.close();
                    resolve();
                });

                ws.on('error', (error) => {
                    clearTimeout(timeout);
                    reject(error);
                });
            });
        });
    });
});
