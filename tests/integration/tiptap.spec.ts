import { randomUUID } from 'node:crypto';

import { DraftPage } from '../../src/drizzle/schema';
import { documentService, pageService } from './test-setup';
import { RealHocuspocusTestClient } from './utils/real-hocuspocus-client';

const baseUrl = 'http://localhost:3000'; // Используем тестовый сервер из test-setup
let docId: string;
let page: DraftPage;

describe('Binary Y.js Updates', () => {
    beforeEach(async () => {
        docId = randomUUID();
        await documentService.createDocumentWithDraftPage(
            {
                serviceId: 'multi-binary-service',
                entityId: 'multi-binary-entity',
                schoolId: 'multi-binary-school',
            },
            docId,
        );

        const draftPages = await pageService.getDraftPagesByDocId(docId);
        page = draftPages[0];
    });

    afterEach(async () => {
        page = null;
        docId = null;
    });

    it.only('должен отправить бинарные обновления и сериализовать их в JSON', async () => {
        // 2. Подключаемся к документу через WebSocket с реальным клиентом
        const client = new RealHocuspocusTestClient({
            url: baseUrl,
            documentName: page.id, // используем pageId как documentName
        });

        await client.connect();
        expect(client.isConnected()).toBe(true);

        // 3. Ждем синхронизации
        await client.waitForSync(3000);

        // 4. Вносим изменения через Y.js обновления

        // Добавляем заголовок
        client.insertHeading('Тестовый заголовок', 1);
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Добавляем параграф с жирным текстом
        client.insertBoldText('Жирный текст');
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Добавляем курсивный текст
        client.insertItalicText('Курсивный текст');
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Добавляем bullet list
        client.insertBulletList(['Первый элемент', 'Второй элемент', 'Третий элемент']);
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Добавляем ordered list
        client.insertOrderedList(['Пункт 1', 'Пункт 2', 'Пункт 3']);
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Добавляем таблицу
        client.insertTable(2, 3);
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Добавляем code block
        client.insertCodeBlock('console.log("Hello, World!");', 'javascript');
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Добавляем blockquote
        client.insertBlockquote('Это цитата');
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Добавляем горизонтальную линию
        client.insertHorizontalRule();
        await new Promise((resolve) => setTimeout(resolve, 500));

        // 5. Проверяем содержимое клиента
        const clientContent = client.getContent();
        expect(clientContent.type).toBe('doc');
        expect(clientContent.content).toBeDefined();
        expect(Array.isArray(clientContent.content)).toBe(true);
        expect(clientContent.content.length).toBeGreaterThan(0);

        // Проверяем, что есть различные типы контента
        const contentTypes = clientContent.content.map((node: any) => node.type);
        expect(contentTypes).toContain('heading');
        expect(contentTypes).toContain('paragraph');
        expect(contentTypes).toContain('bulletList');
        expect(contentTypes).toContain('orderedList');
        expect(contentTypes).toContain('table');
        expect(contentTypes).toContain('codeBlock');
        expect(contentTypes).toContain('blockquote');
        expect(contentTypes).toContain('horizontalRule');

        // 6. Ждем сохранения изменений на сервере
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // 7. Отключаемся от WebSocket
        client.disconnect();

        // 8. Получаем черновую страницу и проверяем, что данные сохранились
        const updatedPage = await pageService.findDraftById(page.id);
        expect(updatedPage).toBeDefined();
        expect(updatedPage.id).toBe(page.id);
        expect(updatedPage.docId).toBe(docId);

        // Проверяем, что ydoc содержит бинарные данные
        // В реальной реализации здесь должны быть бинарные данные Y.js
        // expect(updatedPage.ydoc).toBeDefined();
        // expect(updatedPage.ydoc).toBeInstanceOf(Buffer);
    });

    it('должен правильно обработать множественные бинарные обновления', async () => {
        // 2. Подключаем два клиента
        const client1 = new RealHocuspocusTestClient({
            url: baseUrl,
            documentName: page.id,
        });

        const client2 = new RealHocuspocusTestClient({
            url: baseUrl,
            documentName: page.id,
        });

        await Promise.all([client1.connect(), client2.connect()]);

        expect(client1.isConnected()).toBe(true);
        expect(client2.isConnected()).toBe(true);

        // 3. Ждем синхронизации
        await Promise.all([client1.waitForSync(3000), client2.waitForSync(3000)]);

        // 4. Первый клиент добавляет контент
        client1.insertHeading('Заголовок от клиента 1', 1);
        await new Promise((resolve) => setTimeout(resolve, 500));

        client1.insertText('Текст от первого клиента');
        await new Promise((resolve) => setTimeout(resolve, 500));

        // 5. Второй клиент добавляет контент
        client2.insertHeading('Заголовок от клиента 2', 2);
        await new Promise((resolve) => setTimeout(resolve, 500));

        client2.insertBoldText('Жирный текст от второго клиента');
        await new Promise((resolve) => setTimeout(resolve, 500));

        // 6. Проверяем, что оба клиента видят изменения
        await new Promise((resolve) => setTimeout(resolve, 1000));

        const content1 = client1.getContent();
        const content2 = client2.getContent();

        expect(content1.content).toBeDefined();
        expect(content2.content).toBeDefined();
        expect(content1.content.length).toBeGreaterThan(0);
        expect(content2.content.length).toBeGreaterThan(0);

        // 7. Отключаем клиентов
        client1.disconnect();
        client2.disconnect();

        // 8. Ждем сохранения
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // 9. Проверяем результат через pageService
        const finalPage = await pageService.findDraftById(page.id);
        expect(finalPage).toBeDefined();
        expect(finalPage.id).toBe(page.id);
        expect(finalPage.docId).toBe(docId);
    });

    it('должен сохранить бинарные данные Y.js в базе данных', async () => {
        // 2. Подключаемся и добавляем контент
        const client = new RealHocuspocusTestClient({
            url: baseUrl,
            documentName: page.id,
        });

        await client.connect();
        await client.waitForSync(3000);

        // 3. Добавляем контент
        client.insertHeading('Заголовок для проверки бинарных данных', 1);
        client.insertText('Параграф с текстом для проверки сохранения');
        client.insertBulletList(['Элемент 1', 'Элемент 2']);

        // 4. Получаем бинарное состояние
        const binaryState = client.getBinaryState();
        expect(binaryState).toBeInstanceOf(Uint8Array);
        expect(binaryState.length).toBeGreaterThan(0);

        // 5. Ждем сохранения
        await new Promise((resolve) => setTimeout(resolve, 2000));

        client.disconnect();

        // 6. Проверяем, что данные сохранились
        const savedPage = await pageService.findDraftById(page.id);
        expect(savedPage).toBeDefined();
        expect(savedPage.docId).toBe(docId);

        // В реальной реализации здесь должны быть бинарные данные ydoc
        // expect(savedPage.ydoc).toBeDefined();
        // expect(savedPage.ydoc).toBeInstanceOf(Buffer);
    });
});
