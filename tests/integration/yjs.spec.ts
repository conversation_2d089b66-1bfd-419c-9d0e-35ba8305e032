import { JSONContent } from '@tiptap/core';
import * as Y from 'yjs';

describe('Y.js Specifications', () => {
    it('должен корректно работать с Y.js документом', async () => {
        // 1. Создаем Y.Doc локально
        const localDoc = new Y.Doc();
        const fragment = localDoc.getXmlFragment('default');

        // 2. Добавляем контент в Y.Doc
        const xmlText = new Y.XmlText();
        xmlText.insert(0, 'Тестовый контент');
        fragment.insert(0, [xmlText]);

        // 3. Получаем бинарное состояние
        const binaryState = Y.encodeStateAsUpdate(localDoc);
        expect(binaryState).toBeInstanceOf(Uint8Array);
        expect(binaryState.length).toBeGreaterThan(0);

        // 4. Создаем новый документ и применяем обновление
        const newDoc = new Y.Doc();
        Y.applyUpdate(newDoc, binaryState);

        // 5. Проверяем, что контент сохранился
        const newFragment = newDoc.getXmlFragment('default');
        expect(newFragment.length).toBe(1);

        const firstElement = newFragment.get(0);
        expect(firstElement).toBeDefined();
    });

    it('должен сериализовать Y.js обновления в JSON', async () => {
        // 1. Создаем Y.Doc с контентом
        const doc = new Y.Doc();
        const fragment = doc.getXmlFragment('default');

        // 2. Добавляем различные типы контента
        const text1 = new Y.XmlText();
        text1.insert(0, 'Первый текст');
        fragment.insert(0, [text1]);

        const text2 = new Y.XmlText();
        text2.insert(0, 'Второй текст');
        fragment.insert(1, [text2]);

        // 3. Получаем бинарное состояние
        const binaryState = Y.encodeStateAsUpdate(doc);
        expect(binaryState).toBeInstanceOf(Uint8Array);
        expect(binaryState.length).toBeGreaterThan(0);

        // 4. Создаем новый документ и применяем обновление
        const newDoc = new Y.Doc();
        Y.applyUpdate(newDoc, binaryState);

        // 5. Проверяем, что контент восстановился
        const newFragment = newDoc.getXmlFragment('default');
        expect(newFragment.length).toBe(2);

        // 6. Проверяем содержимое элементов
        const element1 = newFragment.get(0);
        const element2 = newFragment.get(1);
        expect(element1).toBeDefined();
        expect(element2).toBeDefined();

        if (element1 && 'toString' in element1) {
            expect(element1.toString()).toBe('Первый текст');
        }
        if (element2 && 'toString' in element2) {
            expect(element2.toString()).toBe('Второй текст');
        }
    });

    it('должен обработать множественные бинарные обновления от разных источников', async () => {
        // 1. Создаем базовый документ
        const baseDoc = new Y.Doc();
        const baseFragment = baseDoc.getXmlFragment('default');
        const baseText = new Y.XmlText();
        baseText.insert(0, 'Базовый контент');
        baseFragment.insert(0, [baseText]);

        // 2. Создаем два документа на основе базового
        const doc1 = new Y.Doc();
        const doc2 = new Y.Doc();

        // Применяем базовое состояние к обоим документам
        const baseState = Y.encodeStateAsUpdate(baseDoc);
        Y.applyUpdate(doc1, baseState);
        Y.applyUpdate(doc2, baseState);

        // 3. Вносим разные изменения в каждый документ
        const fragment1 = doc1.getXmlFragment('default');
        const text1 = fragment1.get(0) as Y.XmlText;
        text1.insert(text1.length, ' + изменения от клиента 1');

        const fragment2 = doc2.getXmlFragment('default');
        const text2 = fragment2.get(0) as Y.XmlText;
        text2.insert(text2.length, ' + изменения от клиента 2');

        // 4. Получаем инкрементальные обновления
        const update1 = Y.encodeStateAsUpdate(doc1, Y.encodeStateVector(baseDoc));
        const update2 = Y.encodeStateAsUpdate(doc2, Y.encodeStateVector(baseDoc));

        // 5. Проверяем, что обновления не пустые
        expect(update1.length).toBeGreaterThan(0);
        expect(update2.length).toBeGreaterThan(0);

        // 6. Создаем финальный документ и применяем все обновления
        const finalDoc = new Y.Doc();
        Y.applyUpdate(finalDoc, baseState);
        Y.applyUpdate(finalDoc, update1);
        Y.applyUpdate(finalDoc, update2);

        // 7. Проверяем, что merge прошел корректно
        const finalFragment = finalDoc.getXmlFragment('default');
        expect(finalFragment.length).toBe(1);

        const finalText = finalFragment.get(0) as Y.XmlText;
        const finalContent = finalText.toString();

        expect(finalContent).toContain('Базовый контент');
        expect(finalContent).toContain('клиента 1');
        expect(finalContent).toContain('клиента 2');
    });

    it('должен корректно обработать сложные Y.js операции', async () => {
        // 1. Создаем документ с множественными элементами
        const doc = new Y.Doc();
        const fragment = doc.getXmlFragment('default');

        // Добавляем несколько текстовых элементов
        const text1 = new Y.XmlText();
        text1.insert(0, 'Первый параграф');
        fragment.insert(0, [text1]);

        const text2 = new Y.XmlText();
        text2.insert(0, 'Второй параграф');
        fragment.insert(1, [text2]);

        const text3 = new Y.XmlText();
        text3.insert(0, 'Третий параграф');
        fragment.insert(2, [text3]);

        // 2. Получаем бинарное состояние
        const binaryState = Y.encodeStateAsUpdate(doc);
        expect(binaryState).toBeInstanceOf(Uint8Array);
        expect(binaryState.length).toBeGreaterThan(0);

        // 3. Создаем новый документ и восстанавливаем состояние
        const restoredDoc = new Y.Doc();
        Y.applyUpdate(restoredDoc, binaryState);

        // 4. Проверяем, что все элементы восстановились
        const restoredFragment = restoredDoc.getXmlFragment('default');
        expect(restoredFragment.length).toBe(3);

        // 5. Проверяем содержимое каждого элемента
        const element1 = restoredFragment.get(0);
        const element2 = restoredFragment.get(1);
        const element3 = restoredFragment.get(2);

        if (element1 && 'toString' in element1) {
            expect(element1.toString()).toBe('Первый параграф');
        }
        if (element2 && 'toString' in element2) {
            expect(element2.toString()).toBe('Второй параграф');
        }
        if (element3 && 'toString' in element3) {
            expect(element3.toString()).toBe('Третий параграф');
        }
    });

    it('должен обработать инкрементальные обновления Y.js', async () => {
        // 1. Создаем базовый документ
        const doc = new Y.Doc();
        const fragment = doc.getXmlFragment('default');

        const initialText = new Y.XmlText();
        initialText.insert(0, 'Начальный текст');
        fragment.insert(0, [initialText]);

        // 2. Получаем начальное состояние
        const initialState = Y.encodeStateAsUpdate(doc);

        // 3. Добавляем новый контент
        const additionalText = new Y.XmlText();
        additionalText.insert(0, 'Дополнительный текст');
        fragment.insert(1, [additionalText]);

        // 4. Получаем инкрементальное обновление
        const incrementalUpdate = Y.encodeStateAsUpdate(doc, Y.encodeStateVector(new Y.Doc()));
        expect(incrementalUpdate.length).toBeGreaterThan(initialState.length);

        // 5. Создаем новый документ и применяем только инкрементальное обновление
        const newDoc = new Y.Doc();
        Y.applyUpdate(newDoc, incrementalUpdate);

        // 6. Проверяем, что весь контент присутствует
        const newFragment = newDoc.getXmlFragment('default');
        expect(newFragment.length).toBe(2);

        const elem1 = newFragment.get(0);
        const elem2 = newFragment.get(1);

        if (elem1 && 'toString' in elem1) {
            expect(elem1.toString()).toBe('Начальный текст');
        }
        if (elem2 && 'toString' in elem2) {
            expect(elem2.toString()).toBe('Дополнительный текст');
        }
    });

    it('должен правильно обработать конфликты при слиянии Y.js документов', async () => {
        // 1. Создаем базовый документ
        const baseDoc = new Y.Doc();
        const baseFragment = baseDoc.getXmlFragment('default');
        const baseText = new Y.XmlText();
        baseText.insert(0, 'Общий текст');
        baseFragment.insert(0, [baseText]);

        const baseState = Y.encodeStateAsUpdate(baseDoc);

        // 2. Создаем два клиентских документа
        const clientDoc1 = new Y.Doc();
        const clientDoc2 = new Y.Doc();

        Y.applyUpdate(clientDoc1, baseState);
        Y.applyUpdate(clientDoc2, baseState);

        // 3. Каждый клиент вносит изменения в одно и то же место
        const fragment1 = clientDoc1.getXmlFragment('default');
        const text1 = fragment1.get(0) as Y.XmlText;
        text1.insert(0, 'Клиент1: ');

        const fragment2 = clientDoc2.getXmlFragment('default');
        const text2 = fragment2.get(0) as Y.XmlText;
        text2.insert(0, 'Клиент2: ');

        // 4. Получаем обновления от каждого клиента
        const update1 = Y.encodeStateAsUpdate(clientDoc1, Y.encodeStateVector(baseDoc));
        const update2 = Y.encodeStateAsUpdate(clientDoc2, Y.encodeStateVector(baseDoc));

        // 5. Создаем серверный документ и применяем оба обновления
        const serverDoc = new Y.Doc();
        Y.applyUpdate(serverDoc, baseState);
        Y.applyUpdate(serverDoc, update1);
        Y.applyUpdate(serverDoc, update2);

        // 6. Проверяем, что конфликт разрешен детерминированно
        const serverFragment = serverDoc.getXmlFragment('default');
        expect(serverFragment.length).toBe(1);

        const finalText = serverFragment.get(0) as Y.XmlText;
        const finalContent = finalText.toString();

        // Y.js должен детерминированно разрешить конфликт
        expect(finalContent).toContain('Общий текст');
        expect(finalContent.length).toBeGreaterThan('Общий текст'.length);
    });

    it('должен корректно сериализовать Y.js документ в JSON', async () => {
        // 1. Создаем Y.Doc локально с тем же контентом
        const localDoc = new Y.Doc();

        // 2. Создаем Tiptap JSON контент
        const tiptapContent: JSONContent = {
            type: 'doc',
            content: [
                {
                    type: 'heading',
                    attrs: { level: 1 },
                    content: [{ type: 'text', text: 'Тестовый заголовок' }],
                },
                {
                    type: 'paragraph',
                    content: [
                        { type: 'text', text: 'Обычный текст ' },
                        { type: 'text', text: 'жирный текст', marks: [{ type: 'bold' }] },
                        { type: 'text', text: ' и ' },
                        { type: 'text', text: 'курсивный текст', marks: [{ type: 'italic' }] },
                    ],
                },
                {
                    type: 'bulletList',
                    content: [
                        {
                            type: 'listItem',
                            content: [
                                {
                                    type: 'paragraph',
                                    content: [{ type: 'text', text: 'Элемент списка 1' }],
                                },
                            ],
                        },
                        {
                            type: 'listItem',
                            content: [
                                {
                                    type: 'paragraph',
                                    content: [{ type: 'text', text: 'Элемент списка 2' }],
                                },
                            ],
                        },
                    ],
                },
            ],
        };

        // 3. Добавляем контент в Y.Doc
        const fragment = localDoc.getXmlFragment('default');
        const xmlText = new Y.XmlText();
        xmlText.insert(0, JSON.stringify(tiptapContent));
        fragment.insert(0, [xmlText]);

        // 4. Получаем бинарное состояние
        const binaryState = Y.encodeStateAsUpdate(localDoc);
        expect(binaryState).toBeInstanceOf(Uint8Array);
        expect(binaryState.length).toBeGreaterThan(0);

        // 5. Создаем новый документ и применяем обновление
        const newDoc = new Y.Doc();
        Y.applyUpdate(newDoc, binaryState);

        // 6. Проверяем, что контент сохранился
        const newFragment = newDoc.getXmlFragment('default');
        expect(newFragment.length).toBe(1);

        const firstElement = newFragment.get(0);
        expect(firstElement).toBeDefined();

        // 7. Проверяем, что JSON контент можно восстановить
        if (firstElement && 'toString' in firstElement) {
            const restoredContent = firstElement.toString();
            expect(restoredContent).toContain('Тестовый заголовок');
            expect(restoredContent).toContain('bulletList');
        }
    });
});
