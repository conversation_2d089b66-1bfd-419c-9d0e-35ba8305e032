import { randomUUID } from 'node:crypto';
import { NotFoundException } from '@nestjs/common';

import { CreateDocumentDto } from '../../src/modules/editor/application/services/document.service';
import { app, documentService, pageService } from './test-setup';

describe('PageService Integration Tests (New Architecture)', () => {
    let docId: string;
    let createDocDto: CreateDocumentDto;

    beforeAll(async () => {
        expect(app).toBeDefined();
        expect(documentService).toBeDefined();
        expect(pageService).toBeDefined();
    });

    beforeEach(async () => {
        // Создаем новый документ перед каждым тестом
        createDocDto = {
            serviceId: 'test-service',
            entityId: 'test-entity',
            schoolId: 'test-school',
        };
        docId = randomUUID();
        await documentService.createDocumentWithDraftPage(createDocDto, docId);
    });

    afterEach(async () => {
        // Очищаем все черновые страницы после каждого теста
        const draftPages = await pageService.getDraftPagesByDocId(docId);
        for (const page of draftPages) {
            await pageService.deletePage(page.id);
        }
    });

    describe('createDraftPage', () => {
        it('должен создать черновик страницы', async () => {
            const pageId = randomUUID();
            const draftPage = await pageService.createDraftPage(docId, pageId);

            expect(draftPage).toBeDefined();
            expect(draftPage.id).toBe(pageId);
            expect(draftPage.docId).toBe(docId);
            expect(draftPage.ydoc).toBeNull();
            expect(draftPage.createdAt).toBeDefined();
            expect(draftPage.updatedAt).toBeDefined();
        });

        it('должен создать черновик страницы с предустановленным pageId', async () => {
            const customPageId = randomUUID();
            const draftPage = await pageService.createDraftPage(docId, customPageId);

            expect(draftPage).toBeDefined();
            expect(draftPage.id).toBe(customPageId);
            expect(draftPage.docId).toBe(docId);
        });

        it('должен создать документ и получить черновик страницы', async () => {
            const docId = randomUUID();
            await documentService.createDocumentWithDraftPage(
                {
                    serviceId: 'doc-ops-service',
                    entityId: 'doc-ops-entity',
                    schoolId: 'doc-ops-school',
                },
                docId,
            );

            const draftPages = await pageService.getDraftPagesByDocId(docId);
            const page = draftPages[0];
            expect(page).toBeDefined();

            // Get the page back
            const retrievedPage = await pageService.findDraftById(page.id);
            expect(retrievedPage).toBeDefined();
            expect(retrievedPage.id).toBe(page.id);
            expect(retrievedPage.docId).toBe(docId);
        });
    });

    describe('findDraftById', () => {
        it('должен найти существующий черновик страницы', async () => {
            const draftPages = await pageService.getDraftPagesByDocId(docId);
            expect(draftPages).toHaveLength(1);

            const draftPageId = draftPages[0].id;
            const foundPage = await pageService.findDraftById(draftPageId);

            expect(foundPage.id).toBe(draftPageId);
            expect(foundPage.docId).toBe(docId);
        });

        it('должен выбросить NotFoundException для несуществующего черновика', async () => {
            const nonExistentId = randomUUID();
            await expect(pageService.findDraftById(nonExistentId)).rejects.toThrow(NotFoundException);
            await expect(pageService.findDraftById(nonExistentId)).rejects.toThrow(
                `Draft page with ID ${nonExistentId} not found`,
            );
        });
    });

    describe('findDraftByIdOrNull', () => {
        it('должен найти существующий черновик страницы', async () => {
            const draftPages = await pageService.getDraftPagesByDocId(docId);
            const draftPageId = draftPages[0].id;

            const foundPage = await pageService.findDraftByIdOrNull(draftPageId);
            expect(foundPage).toBeDefined();
            expect(foundPage.id).toBe(draftPageId);
            expect(foundPage.docId).toBe(docId);
        });

        it('должен вернуть null для несуществующего черновика', async () => {
            const nonExistentId = randomUUID();
            const result = await pageService.findDraftByIdOrNull(nonExistentId);
            expect(result).toBeNull();
        });
    });

    describe('addPageToDocument', () => {
        it('должен добавить новую страницу к документу', async () => {
            let draftPages = await pageService.getDraftPagesByDocId(docId);
            expect(draftPages).toHaveLength(1);

            const newPage = await pageService.addPageToDocument(docId);
            expect(newPage).toBeDefined();
            expect(newPage.docId).toBe(docId);

            draftPages = await pageService.getDraftPagesByDocId(docId);
            expect(draftPages).toHaveLength(2);
        });
    });

    describe('deletePage', () => {
        it('должен удалить страницу из обеих таблиц', async () => {
            await pageService.addPageToDocument(docId);
            await documentService.publishDocument(docId);

            let draftPages = await pageService.getDraftPagesByDocId(docId);
            let publicPages = await pageService.getPublicPagesByDocId(docId);
            expect(draftPages).toHaveLength(2);
            expect(publicPages).toHaveLength(2);

            const pageToDelete = draftPages[0].id;
            await pageService.deletePage(pageToDelete);

            draftPages = await pageService.getDraftPagesByDocId(docId);
            publicPages = await pageService.getPublicPagesByDocId(docId);
            expect(draftPages).toHaveLength(1);
            expect(publicPages).toHaveLength(1);
            expect(draftPages.find((p) => p.id === pageToDelete)).toBeUndefined();
            expect(publicPages.find((p) => p.id === pageToDelete)).toBeUndefined();
        });
    });

    describe('publishDocumentPages', () => {
        it('должен опубликовать страницы документа', async () => {
            await pageService.addPageToDocument(docId);
            await pageService.addPageToDocument(docId);

            const draftPages = await pageService.getDraftPagesByDocId(docId);
            let publicPages = await pageService.getPublicPagesByDocId(docId);
            expect(draftPages).toHaveLength(3);
            expect(publicPages).toHaveLength(0);

            await pageService.publishDocumentPages(docId);

            publicPages = await pageService.getPublicPagesByDocId(docId);
            expect(publicPages).toHaveLength(3);
            expect(publicPages.map((p) => p.id).sort()).toEqual(draftPages.map((p) => p.id).sort());
        });

        it('должен корректно обработать документ без черновых страниц', async () => {
            const draftPages = await pageService.getDraftPagesByDocId(docId);
            for (const page of draftPages) {
                await pageService.deletePage(page.id);
            }

            await expect(pageService.publishDocumentPages(docId)).resolves.not.toThrow();

            const publicPages = await pageService.getPublicPagesByDocId(docId);
            expect(publicPages).toHaveLength(0);
        });
    });

    describe('updateDraftPage', () => {
        it('должен обновить черновик страницы', async () => {
            const draftPages = await pageService.getDraftPagesByDocId(docId);
            const pageId = draftPages[0].id;

            const updateData = {
                ydoc: Buffer.from('test ydoc data'),
            };
            await pageService.updateDraftPage(pageId, updateData);

            const updatedPage = await pageService.findDraftById(pageId);
            expect(updatedPage.ydoc).toEqual(updateData.ydoc);
        });
    });

    describe('getPublicPagesByDocId', () => {
        it('должен получить публичные страницы документа', async () => {
            await pageService.addPageToDocument(docId);
            await documentService.publishDocument(docId);

            const publicPages = await pageService.getPublicPagesByDocId(docId);
            expect(publicPages).toHaveLength(2);
            publicPages.forEach((page) => {
                expect(page.docId).toBe(docId);
                expect(page.id).toBeDefined();
                expect(page.createdAt).toBeDefined();
                expect(page.updatedAt).toBeDefined();
            });
        });

        it('должен вернуть пустой массив для документа без публичных страниц', async () => {
            const publicPages = await pageService.getPublicPagesByDocId(docId);
            expect(publicPages).toHaveLength(0);
        });
    });

    describe('getDraftPagesByDocId', () => {
        it('должен получить черновые страницы документа', async () => {
            await pageService.addPageToDocument(docId);
            await pageService.addPageToDocument(docId);

            const draftPages = await pageService.getDraftPagesByDocId(docId);
            expect(draftPages).toHaveLength(3);
            draftPages.forEach((page) => {
                expect(page.docId).toBe(docId);
                expect(page.id).toBeDefined();
                expect(page.createdAt).toBeDefined();
                expect(page.updatedAt).toBeDefined();
            });
        });
    });
});
