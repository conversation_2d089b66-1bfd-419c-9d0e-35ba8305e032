import { Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { IsNotEmpty, IsString } from 'class-validator';
import { v7 as uuidv7 } from 'uuid';

import { DRIZZLE_DB } from '../../../../drizzle/drizzle.module';
import { DrizzleDB } from '../../../../drizzle/drizzle.types';
import { Doc, InsertableDoc } from '../../../../drizzle/schema/docs';
import { DOC_REPO, DRAFT_PAGE_REPO, PUBLIC_PAGE_REPO } from '../../injects';
import {
    CreateDocumentRequest,
    CreateDocumentResponse,
    IDocumentRepo,
    RemoveManyDocumentsResponse,
} from '../repositories/document-repo.interface';
import { IDraftPageRepo } from '../repositories/draft-page-repo.interface';
import { IPublicPageRepo } from '../repositories/public-page-repo.interface';
import { PageService } from './page.service';

export class CreateDocumentDto {
    @IsNotEmpty()
    @IsString()
    schoolId: string;

    @IsNotEmpty()
    @IsString()
    serviceId: string;

    @IsNotEmpty()
    @IsString()
    entityId: string;
}

@Injectable()
export class DocumentService {
    private readonly logger = new Logger(DocumentService.name);

    constructor(
        @Inject(DOC_REPO)
        private docRepo: IDocumentRepo,
        @Inject(PUBLIC_PAGE_REPO)
        private publicPageRepo: IPublicPageRepo,
        @Inject(DRAFT_PAGE_REPO)
        private draftPageRepo: IDraftPageRepo,
        @Inject(DRIZZLE_DB)
        private db: DrizzleDB,
        private pageService: PageService,
    ) {}

    async findByIdOrThrow(docId: string): Promise<Doc> {
        const doc = await this.docRepo.findById(docId);
        if (!doc) {
            throw new NotFoundException(`Документ не найден (docId: ${docId})`);
        }
        return doc;
    }

    /**
     * Поиск существующего документа по ID
     */
    async findByIdOrNull(docId: string): Promise<Doc | null> {
        try {
            const result = await this.docRepo.findById(docId);
            if (!result) {
                this.logger.debug(`Документ не найден (docId: ${docId})`);
                return null;
            }
            this.logger.debug(`Найден документ с ID: ${docId}`);
            return result;
        } catch (error) {
            this.logger.error(error, `Ошибка загрузки документа (docId: ${docId}):`);
        }
    }

    /**
     * Создать документ с первой черновой страницей (в транзакции)
     */
    async createDocumentWithDraftPage(createDocDto: CreateDocumentDto, docId: string): Promise<void> {
        const insertableData: InsertableDoc = {
            id: docId,
            serviceId: createDocDto.serviceId,
            entityId: createDocDto.entityId,
            schoolId: createDocDto.schoolId,
        };

        try {
            return await this.db.transaction(async (trx) => {
                const doc = await this.docRepo.insertDoc(insertableData, trx);
                if (!doc) {
                    throw new Error('Ошибка создания документа');
                }
                const pageId = uuidv7();
                await this.pageService.createDraftPage(docId, pageId, trx);

                this.logger.debug(`Создан документ с ID: ${docId}`);
            });
        } catch (createError) {
            this.logger.error(createError, `Ошибка создания документа для ${docId}:`);
            throw createError;
        }
    }

    /**
     * Опубликовать документ
     */
    async publishDocument(docId: string): Promise<void> {
        this.logger.log(`Публикуем документ ${docId}`);

        await this.findByIdOrThrow(docId);
        await this.pageService.publishDocumentPages(docId);

        this.logger.log(`Документ ${docId} опубликован`);
    }

    /**
     * Удалить записи документа из базы
     */
    async forceDelete(docId: string): Promise<void> {
        this.logger.log(`Удаление документа ${docId}`);

        return await this.db.transaction(async (trx) => {
            await this.publicPageRepo.deleteByDocumentId(docId, trx);
            await this.draftPageRepo.deleteByDocumentId(docId, trx);
            await this.docRepo.deleteDoc(docId, trx);

            this.logger.log(`Удален документ ${docId}`);
        });
    }

    async createManyDocs(docs: CreateDocumentRequest[]): Promise<CreateDocumentResponse[]> {
        this.logger.log(`Creating ${docs.length} docs with draft pages`);

        const results: CreateDocumentResponse[] = [];

        try {
            for (const docRequest of docs) {
                const createDto: CreateDocumentDto = {
                    serviceId: docRequest.serviceId,
                    entityId: docRequest.entityId,
                    schoolId: docRequest.schoolId,
                };

                const documentId = uuidv7();
                await this.createDocumentWithDraftPage(createDto, documentId);
                results.push({
                    ...createDto,
                    docId: documentId,
                });
            }

            this.logger.log(`Создано ${results.length} документов`);
            return results;
        } catch (error) {
            this.logger.error(error, 'Ошибка пакетного создания документов');
            throw error;
        }
    }

    async forceDeleteManyDocs(docIds: string[]): Promise<RemoveManyDocumentsResponse> {
        this.logger.log(`Удаление ${docIds.length} документов`);

        const removedDocIds: string[] = [];

        try {
            for (const docId of docIds) {
                await this.forceDelete(docId);
                removedDocIds.push(docId);
            }

            this.logger.log(`Удалено ${removedDocIds.length} документов`);

            return {
                success: true,
                removedDocIds,
            };
        } catch (error) {
            this.logger.error(error, 'Ошибка пакетного удаления документов');
            throw error;
        }
    }
}
