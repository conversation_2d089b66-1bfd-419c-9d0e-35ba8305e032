import { DrizzleTransaction } from '../../../../drizzle/drizzle.types';
import { DraftPage, InsertableDraftPage, UpdatableDraftPage } from '../../../../drizzle/schema/draft-pages';

export interface IDraftPageRepo {
    /**
     * Найти черновик страницы по ID
     */
    findById(pageId: string, trx?: DrizzleTransaction): Promise<DraftPage | undefined>;

    /**
     * Найти черновики страниц по docId
     */
    findByDocId(docId: string, trx?: DrizzleTransaction): Promise<DraftPage[]>;

    /**
     * Обновить одну черновик страницы
     */
    updatePage(updatablePage: UpdatableDraftPage, pageId: string, trx?: DrizzleTransaction): Promise<void>;

    /**
     * Обновить несколько черновиков страниц
     */
    updatePages(updatePageData: UpdatableDraftPage, pageIds: string[], trx?: DrizzleTransaction): Promise<void>;

    /**
     * Вставить новый черновик страницы
     */
    insertPage(insertablePage: InsertableDraftPage, trx?: DrizzleTransaction): Promise<DraftPage | undefined>;

    /**
     * Вставить несколько черновиков страниц
     */
    insertManyPages(insertablePages: InsertableDraftPage[], trx?: DrizzleTransaction): Promise<DraftPage[]>;

    /**
     * Удалить черновик страницы по ID
     */
    deletePage(pageId: string, trx?: DrizzleTransaction): Promise<void>;

    /**
     * Удалить несколько черновиков страниц по ID
     */
    deleteByDocumentId(docId: string, trx?: DrizzleTransaction): Promise<void>;

    /**
     * Проверить наличие черновика страницы в БД
     */
    exists(pageId: string): Promise<boolean>;
}
