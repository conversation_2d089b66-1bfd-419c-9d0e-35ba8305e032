import { Logger } from '@nestjs/common';
import { Args, ID, Mutation, Query, Resolver } from '@skillspace/graphql';

import { DocumentService } from '../../application/services/document.service';
import { PageService } from '../../application/services/page.service';
import { DocumentOutput } from './output/document.output';
import { DocumentPagesOutput } from './output/document-pages.output';
import { PageOutput } from './output/page.output';

@Resolver(() => PageOutput)
export class PageResolver {
    private readonly logger = new Logger(PageResolver.name);

    constructor(
        private readonly docService: DocumentService,
        private readonly pageService: PageService,
    ) {}

    @Query(() => DocumentOutput, { name: 'document' })
    async getDocument(
        @Args('docId', { type: () => ID })
        docId: string,
    ): Promise<DocumentOutput> {
        this.logger.log(`Getting document by ID: ${docId}`);
        const doc = await this.docService.findByIdOrThrow(docId);
        const pages = await this.pageService.getPublicPagesByDocId(docId);

        return {
            id: doc.id,
            pages: pages.map((page) => ({
                id: page.id,
                content: (page.content as object) || {},
                createdAt: page.createdAt,
                updatedAt: page.updatedAt,
            })),
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
        };
    }

    @Mutation(() => Boolean, { name: 'publishDocument' })
    async publishDocument(
        @Args('docId', { type: () => ID })
        docId: string,
    ): Promise<boolean> {
        await this.docService.publishDocument(docId);
        return true;
    }

    @Mutation(() => DocumentPagesOutput, { name: 'addPage' })
    async addPage(
        @Args('docId', { type: () => ID })
        docId: string,
    ): Promise<DocumentPagesOutput> {
        this.logger.log(`Adding page into doc: ${docId}`);
        await this.pageService.addPageToDocument(docId);

        const doc = await this.docService.findByIdOrThrow(docId);
        const pages = await this.pageService.getDraftPagesByDocId(docId);

        return {
            id: docId,
            draftPageIds: pages.map((page) => page.id),
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
        };
    }

    @Mutation(() => Boolean, { name: 'deletePage' })
    async deletePage(
        @Args('pageId', { type: () => ID })
        pageId: string,
    ): Promise<boolean> {
        await this.pageService.deletePage(pageId);
        return true;
    }
}
