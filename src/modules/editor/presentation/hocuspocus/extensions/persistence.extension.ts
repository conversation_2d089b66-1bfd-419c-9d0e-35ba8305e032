import { Extension, onLoadDocumentPayload, onStoreDocumentPayload } from '@hocuspocus/server';
import { Inject, Injectable, Logger } from '@nestjs/common';
import * as Y from 'yjs';

import { DRIZZLE_DB } from '../../../../../drizzle/drizzle.module';
import { DrizzleDB } from '../../../../../drizzle/drizzle.types';
import { executeTx } from '../../../../../drizzle/drizzle.utils';
import { IDraftPageRepo } from '../../../application/repositories/draft-page-repo.interface';
import { PageService } from '../../../application/services/page.service';
import { DRAFT_PAGE_REPO } from '../../../injects';

@Injectable()
export class PersistenceExtension implements Extension {
    private readonly logger = new Logger(PersistenceExtension.name);

    constructor(
        private readonly pageService: PageService,
        @Inject(DRAFT_PAGE_REPO) private readonly draftRepo: IDraftPageRepo,
        @Inject(DRIZZLE_DB) private readonly db: DrizzleDB,
    ) {}

    public async onLoadDocument(data: onLoadDocumentPayload) {
        const { documentName, document } = data;
        const pageId = documentName;

        if (!document.isEmpty('default')) {
            return;
        }

        // if (!documentName) {
        //     return new Y.Doc();
        // }

        const page = await this.pageService.findDraftByIdOrNull(pageId);
        if (!page) {
            this.logger.warn('draft page not found');
            return;
        }

        if (page.ydoc) {
            this.logger.debug(`ydoc loaded from db: ${pageId}`);
            const ydoc = new Y.Doc();
            Y.applyUpdate(ydoc, page.ydoc);
            return ydoc;
        }

        // TODO: в будущем не создаем документ через ws
        this.logger.debug(`Создание нового Y.Doc для: ${pageId}`);
        return new Y.Doc();
    }

    public async onStoreDocument(data: onStoreDocumentPayload) {
        const { documentName, document } = data;

        if (!documentName) {
            return;
        }

        const pageId = documentName;

        const ydocState = Buffer.from(Y.encodeStateAsUpdate(document));

        // let page = await this.pageService.findByIdOrNull(pageId);
        // if (this.hasDataChanged(page, ydocState, pageId)) {
        //     await this.pageService.update(pageId, { });
        // }

        try {
            await executeTx(this.db, async (trx) => {
                const page = await this.draftRepo.findById(pageId, trx);

                if (!page) {
                    this.logger.error(`Draft page with id ${pageId} not found`);
                    return;
                }

                // Обновляем только ydoc в черновике страницы
                await this.draftRepo.updatePage(
                    {
                        ydoc: ydocState,
                    },
                    pageId,
                    trx,
                );

                this.logger.debug(`Draft page updated: ${pageId}`);
            });
        } catch (err) {
            this.logger.error(`Failed to update draft page ${pageId}`, err);
        }
    }
}
