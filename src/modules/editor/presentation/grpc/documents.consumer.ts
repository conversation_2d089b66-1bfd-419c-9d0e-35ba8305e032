import { Injectable } from '@nestjs/common';
import { GrpcSubscribe, ServiceRequest, ServiceResponse } from '@skillspace/grpc';

import { CreateDocumentRequest } from '../../application/repositories/document-repo.interface';
import { DocumentService } from '../../application/services/document.service';

@Injectable()
export class DocManagement {
    constructor(private readonly docsService: DocumentService) {}

    @GrpcSubscribe('EditorService', 'CreateManyDocs', 'v1')
    public async createManyDocs(
        data: ServiceRequest<'EditorService', 'CreateManyDocs', 'v1'>,
    ): Promise<ServiceResponse<'EditorService', 'CreateManyDocs', 'v1'>> {
        const docs = await this.docsService.createManyDocs(data.docs as CreateDocumentRequest[]);
        return { docs };
    }

    @GrpcSubscribe('EditorService', 'RemoveManyDocs', 'v1')
    public async RemoveManyDocs(
        data: ServiceRequest<'EditorService', 'RemoveManyDocs', 'v1'>,
    ): Promise<ServiceResponse<'EditorService', 'RemoveManyDocs', 'v1'>> {
        return await this.docsService.forceDeleteManyDocs(data.docIds);
    }
}
