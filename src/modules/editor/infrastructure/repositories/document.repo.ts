import { Inject, Injectable } from '@nestjs/common';
import { eq, inArray } from 'drizzle-orm';

import { DRIZZLE_DB } from '../../../../drizzle/drizzle.module';
import { DrizzleDB, DrizzleTransaction } from '../../../../drizzle/drizzle.types';
import { dbOrTx } from '../../../../drizzle/drizzle.utils';
import { Doc, docs, InsertableDoc, UpdatableDoc } from '../../../../drizzle/schema/docs';
import { IDocumentRepo } from '../../application/repositories/document-repo.interface';

@Injectable()
export class DocRepo implements IDocumentRepo {
    constructor(@Inject(DRIZZLE_DB) private readonly db: DrizzleDB) {}

    async findById(docId: string, trx?: DrizzleTransaction): Promise<Doc | undefined> {
        const db = dbOrTx(this.db, trx);
        const result = await db.select().from(docs).where(eq(docs.id, docId)).limit(1);
        return result[0];
    }

    async insertDoc(insertableDoc: InsertableDoc, trx?: DrizzleTransaction): Promise<Doc | undefined> {
        const db = dbOrTx(this.db, trx);
        const result = await db.insert(docs).values(insertableDoc).returning().execute();
        return result?.[0];
    }

    async updateDoc(updatableDoc: UpdatableDoc, docId: string, trx?: DrizzleTransaction): Promise<void> {
        const db = dbOrTx(this.db, trx);
        await db.update(docs).set(updatableDoc).where(eq(docs.id, docId)).execute();
    }

    async insertManyDocs(insertableDocs: InsertableDoc[], trx?: DrizzleTransaction): Promise<Doc[]> {
        if (insertableDocs.length === 0) {
            return [];
        }

        const db = dbOrTx(this.db, trx);
        const result = await db.insert(docs).values(insertableDocs).returning().execute();
        return result;
    }

    async deleteDoc(docId: string, trx?: DrizzleTransaction): Promise<void> {
        const db = dbOrTx(this.db, trx);
        await db.delete(docs).where(eq(docs.id, docId)).execute();
    }

    async deleteManyDocs(docIds: string[], trx?: DrizzleTransaction): Promise<void> {
        if (docIds.length === 0) {
            return;
        }

        const db = dbOrTx(this.db, trx);
        await db.delete(docs).where(inArray(docs.id, docIds)).execute();
    }

    async exists(docId: string): Promise<boolean> {
        const result = await this.db.select({ id: docs.id }).from(docs).where(eq(docs.id, docId)).limit(1).execute();

        return result.length > 0;
    }
}
