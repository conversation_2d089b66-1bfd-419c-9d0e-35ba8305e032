import { pgTable, uuid, varchar } from 'drizzle-orm/pg-core';
import { customType } from 'drizzle-orm/pg-core';

import { temporalMixin } from './mixins/temporal.mixin';

const bytea = customType<{ data: Buffer; notNull: false; default: false }>({
    dataType() {
        return 'bytea';
    },
    toDriver(value: Buffer): Buffer {
        return value;
    },
    fromDriver(value: Buffer): Buffer {
        return value;
    },
});

export const draftPages = pgTable('draft_pages', {
    id: uuid('id').primaryKey(),
    docId: varchar('doc_id').notNull(),
    ydoc: bytea('ydoc'),
    ...temporalMixin,
});

export type DraftPage = typeof draftPages.$inferSelect;
export type InsertableDraftPage = typeof draftPages.$inferInsert;
export type UpdatableDraftPage = Partial<Omit<InsertableDraftPage, 'id'>>;
