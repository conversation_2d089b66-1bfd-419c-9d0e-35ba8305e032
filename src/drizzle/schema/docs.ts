import { pgTable, uuid, varchar } from 'drizzle-orm/pg-core';

import { temporalMixin } from './mixins/temporal.mixin';

export const docs = pgTable('docs', {
    id: uuid('id').primaryKey(),
    schoolId: varchar('school_id').notNull(),
    serviceId: varchar('service_id').notNull(), // webinars, courses
    entityId: varchar('entity_id').notNull(), // doc, page e.t.c.

    ...temporalMixin,
});

export type Doc = typeof docs.$inferSelect;
export type InsertableDoc = typeof docs.$inferInsert;
export type UpdatableDoc = Partial<Omit<InsertableDoc, 'id'>>;
