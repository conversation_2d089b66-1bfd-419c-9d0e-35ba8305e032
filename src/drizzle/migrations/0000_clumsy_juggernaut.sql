CREATE TABLE "docs" (
	"id" uuid PRIMARY KEY NOT NULL,
	"school_id" varchar NOT NULL,
	"service_id" varchar NOT NULL,
	"entity_id" varchar NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "draft_pages" (
	"id" uuid PRIMARY KEY NOT NULL,
	"doc_id" varchar NOT NULL,
	"ydoc" "bytea",
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "public_pages" (
	"id" uuid PRIMARY KEY NOT NULL,
	"doc_id" varchar NOT NULL,
	"content" jsonb,
	"text_content" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
